import os
import shutil
import subprocess
import sys
import datetime
import logging
import queue

# --- Global variables for GUI logging (can be refactored/removed if GUI is deprecated) ---
g_log_text_area = None
g_root_tk_instance = None
g_log_queue = None

# Custom handler to send logs to a queue
class QueueHandler(logging.Handler):
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        self.log_queue.put(self.format(record))

def init_logging(log_text_area=None, root_tk_instance=None, log_queue_api=None):
    """Initializes the logging system."""
    global g_log_text_area, g_root_tk_instance, g_log_queue
    g_log_text_area = log_text_area # For potential GUI use
    g_root_tk_instance = root_tk_instance # For potential GUI use
    g_log_queue = log_queue_api

    logger = logging.getLogger("nexus")
    logger.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    # Console handler (always on)
    ch = logging.StreamHandler(sys.stdout)
    ch.setFormatter(formatter)
    logger.addHandler(ch)

    # Queue handler for the API
    if g_log_queue:
        qh = QueueHandler(g_log_queue)
        qh.setFormatter(formatter)
        logger.addHandler(qh)

    # --- Deprecated GUI TextArea logging ---
    # This part needs to be refactored if GUI is to be maintained alongside API.
    # For now, direct Tkinter updates from here are problematic.
    # A better approach would be for the GUI to also consume the g_log_queue.
    if g_log_text_area and g_root_tk_instance:
        logger.info("GUI log text area provided, but direct updates from logger are deprecated. GUI should consume the log queue.")

def log_message(message, level=logging.INFO):
    """Logs a message using the configured logger."""
    logger = logging.getLogger("nexus")
    logger.log(level, message)

def backup_file(file_path):
    """Creates a timestamped backup of a file if it exists."""
    if os.path.exists(file_path):
        try:
            timestamp = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
            backup_path = f"{file_path}.{timestamp}.bak"
            shutil.copy2(file_path, backup_path)
            log_message(f"Backed up {file_path} to {backup_path}")
        except Exception as e:
            log_message(f"Warning: Could not back up {file_path}: {e}", level=logging.WARNING)

def run_command(command, check=True, shell=False, capture_output=False, text=False):
    log_message(f"Running command: {' '.join(command) if isinstance(command, list) else command}")
    try:
        process = subprocess.run(
            command, 
            check=check, 
            shell=shell, 
            capture_output=capture_output, 
            text=text
        )
        stdout_res = process.stdout.strip() if process.stdout and capture_output and text else (process.stdout.decode('utf-8', 'replace').strip() if process.stdout and capture_output else "")
        stderr_res = process.stderr.strip() if process.stderr and capture_output and text else (process.stderr.decode('utf-8', 'replace').strip() if process.stderr and capture_output else "")
        
        if stdout_res:
            log_message(f"  stdout: {stdout_res}")
        if stderr_res:
            log_message(f"  stderr: {stderr_res}")
            # No specific action for check and returncode != 0 here, error is raised by subprocess.run
            
        if capture_output:
            return stdout_res, stderr_res
        return True
        
    except subprocess.CalledProcessError as e:
        err_msg = f"Error executing command: {' '.join(e.cmd) if isinstance(e.cmd, list) else e.cmd}\n"
        err_msg += f"Return code: {e.returncode}\n"
        if e.stdout:
            err_msg += f"Stdout: {e.stdout.decode('utf-8', 'replace').strip()}\n"
        if e.stderr:
            err_msg += f"Stderr: {e.stderr.decode('utf-8', 'replace').strip()}\n"
        log_message(err_msg, level=logging.ERROR)
        raise
    except FileNotFoundError:
        err_msg = f"Error: Command not found: {command[0] if isinstance(command, list) else command.split()[0]}\nEnsure the required programs are installed and in PATH."
        log_message(err_msg, level=logging.ERROR)
        raise

def check_root_privileges():
    if os.geteuid() != 0:
        msg = "Error: This script requires root privileges to run. Please run with sudo."
        log_message(msg, level=logging.ERROR)
        raise PermissionError(msg)
    log_message("Root privileges confirmed.")
