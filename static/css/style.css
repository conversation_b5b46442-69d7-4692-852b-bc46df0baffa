body {
    font-family: sans-serif;
    margin: 0;
    background-color: #222;
    color: #eee;
}

header {
    background-color: #333;
    color: #fff;
    padding: 1rem 0;
    text-align: center;
}

nav ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    text-align: center;
    background-color: #555;
}

nav ul li {
    display: inline;
}

nav ul li a {
    text-decoration: none;
    color: white;
    padding: 1rem;
    display: inline-block;
}

nav ul li a:hover {
    background-color: #777;
}

.container {
    width: 80%;
    margin: 2rem auto;
    padding: 1rem;
    background-color: #333;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

h1, h2 {
    color: #eee;
}

form label {
    display: block;
    margin-top: 0.5rem;
    margin-bottom: 0.2rem;
    font-weight: bold;
}

form input[type="text"],
form select {
    width: 100%;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    border: 1px solid #555;
    background-color: #444;
    color: #eee;
    box-sizing: border-box; /* Ensures padding doesn't expand width */
}

form button {
    background-color: #4CAF50;
    color: white;
    padding: 0.7rem 1.5rem;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    margin-top: 1rem;
}

form button:hover {
    background-color: #45a049;
}

small.form-text.text-muted {
    display: block; /* Already in inline style, but good to have in CSS */
    font-size: 0.85em;
    color: #bbb; /* A lighter muted color for dark theme */
    margin-top: -0.25rem; /* Keep adjustments if they work well */
    margin-bottom: 0.5rem; /* Keep adjustments */
}

.status-message {
    padding: 1rem;
    margin-top: 1rem;
    border-radius: 4px;
}

.status-message.success {
    background-color: #4CAF50;
    color: #fff;
    border: 1px solid #4CAF50;
}

.status-message.error {
    background-color: #f44336;
    color: #fff;
    border: 1px solid #f44336;
}

.log-container {
    white-space: pre-wrap;
    font-family: monospace;
    background-color: #444;
    color: #eee;
    border: 1px solid #555;
    padding: 10px;
    height: 600px;
    overflow-y: scroll;
}

.section {
    background-color: #333;
    border: 1px solid #444;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
}

.section h3 {
    margin-top: 0;
    color: #eee;
    border-bottom: 1px solid #555;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

#connectedClientsList {
    list-style-type: none;
    padding-left: 0;
}

#connectedClientsList li {
    background-color: #444;
    border: 1px solid #555;
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 3px;
}

textarea {
    width: 100%;
    box-sizing: border-box;
    background-color: #444;
    color: #eee;
    border: 1px solid #555;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
}
