import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext, messagebox
import threading

from nexus_config import load_config, save_config, DEFAULT_CONFIG
from nexus_core import (
    perform_pxe_setup, 
    start_client_listener, stop_client_listener, CLIENT_LISTENER_PORT,
    send_data_to_client, broadcast_data_to_all_clients, connected_clients as core_connected_clients
)
from nexus_utils import init_logging, log_message, check_root_privileges

class NexusApp:
    def __init__(self, master):
        self.master = master
        master.title("Nexus PXE Server Setup")
        master.geometry("1200x700")

        self.config = load_config()

        # --- Main frame ---
        main_frame = ttk.Frame(master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # --- Configuration Frame ---
        config_frame = ttk.LabelFrame(main_frame, text="Configuration", padding="10")
        config_frame.pack(fill=tk.X, pady=5)

        # --- ISO Path ---
        ttk.Label(config_frame, text="ISO Path:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.iso_path_var = tk.StringVar(value=self.config.get("iso_path", ""))
        self.iso_entry = ttk.Entry(config_frame, textvariable=self.iso_path_var, width=60)
        self.iso_entry.grid(row=0, column=1, sticky=tk.EW, pady=2, padx=5)
        self.browse_button = ttk.Button(config_frame, text="Browse...", command=self.browse_iso)
        self.browse_button.grid(row=0, column=2, sticky=tk.E, pady=2)

        # --- Distro Type ---
        ttk.Label(config_frame, text="Distribution Type:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.distro_var = tk.StringVar(value=self.config.get("distro_type", "Fedora"))
        self.distro_combobox = ttk.Combobox(config_frame, textvariable=self.distro_var, values=["Fedora", "Mint"])
        self.distro_combobox.grid(row=1, column=1, sticky=tk.W, pady=2, padx=5)

        # --- Network Interface ---
        ttk.Label(config_frame, text="Network Interface:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.net_iface_var = tk.StringVar(value=self.config.get("network_interface", ""))
        self.net_iface_entry = ttk.Entry(config_frame, textvariable=self.net_iface_var, width=30)
        self.net_iface_entry.grid(row=2, column=1, sticky=tk.W, pady=2, padx=5)
        ttk.Label(config_frame, text="(Leave blank to auto-detect)").grid(row=2, column=2, sticky=tk.W, pady=2, padx=5)

        # --- Advanced Options ---
        ttk.Label(config_frame, text="TFTP Root:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.tftp_root_var = tk.StringVar(value=self.config.get("tftp_root", DEFAULT_CONFIG["tftp_root"]))
        self.tftp_root_entry = ttk.Entry(config_frame, textvariable=self.tftp_root_var, width=60)
        self.tftp_root_entry.grid(row=3, column=1, columnspan=2, sticky=tk.EW, pady=2, padx=5)

        ttk.Label(config_frame, text="HTTP Root:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.http_root_var = tk.StringVar(value=self.config.get("http_root", DEFAULT_CONFIG["http_root"]))
        self.http_root_entry = ttk.Entry(config_frame, textvariable=self.http_root_var, width=60)
        self.http_root_entry.grid(row=4, column=1, columnspan=2, sticky=tk.EW, pady=2, padx=5)
        
        ttk.Label(config_frame, text="ISO Mount Point:").grid(row=5, column=0, sticky=tk.W, pady=2)
        self.iso_mount_point_var = tk.StringVar(value=self.config.get("iso_mount_point", DEFAULT_CONFIG["iso_mount_point"]))
        self.iso_mount_point_entry = ttk.Entry(config_frame, textvariable=self.iso_mount_point_var, width=60)
        self.iso_mount_point_entry.grid(row=5, column=1, columnspan=2, sticky=tk.EW, pady=2, padx=5)

        config_frame.columnconfigure(1, weight=1)

        # --- Log Area Frame ---
        log_frame = ttk.LabelFrame(main_frame, text="Logs", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.log_text_area = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=15, state='disabled')
        self.log_text_area.pack(fill=tk.BOTH, expand=True)
        init_logging(self.log_text_area, master)

        # --- Buttons Frame ---
        buttons_frame = ttk.Frame(main_frame, padding="10")
        buttons_frame.pack(fill=tk.X)

        self.start_button = ttk.Button(buttons_frame, text="Start PXE Setup", command=self.start_setup_thread)
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.save_button = ttk.Button(buttons_frame, text="Save Configuration", command=self.save_gui_config)
        self.save_button.pack(side=tk.LEFT, padx=5)
        
        self.exit_button = ttk.Button(buttons_frame, text="Exit", command=self.on_exit)
        self.exit_button.pack(side=tk.RIGHT, padx=5)

        # --- Client Communication Frame ---
        client_comm_frame = ttk.LabelFrame(main_frame, text="Client Communication", padding="10")
        client_comm_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        client_controls_frame = ttk.Frame(client_comm_frame)
        client_controls_frame.pack(fill=tk.X, pady=5)

        self.client_listener_status_var = tk.StringVar(value="Listener: Stopped")
        self.client_listener_status_label = ttk.Label(client_controls_frame, textvariable=self.client_listener_status_var)
        self.client_listener_status_label.pack(side=tk.LEFT, padx=5)

        self.toggle_listener_button = ttk.Button(client_controls_frame, text="Start Client Listener", command=self.toggle_client_listener_service)
        self.toggle_listener_button.pack(side=tk.LEFT, padx=5)

        clients_display_frame = ttk.Frame(client_comm_frame)
        clients_display_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        ttk.Label(clients_display_frame, text="Connected Clients:").pack(anchor=tk.W)
        self.connected_clients_listbox = tk.Listbox(clients_display_frame, height=6, exportselection=False)
        self.connected_clients_listbox.pack(fill=tk.BOTH, expand=True, side=tk.LEFT, padx=(0,5))

        clients_scrollbar = ttk.Scrollbar(clients_display_frame, orient=tk.VERTICAL, command=self.connected_clients_listbox.yview)
        self.connected_clients_listbox.config(yscrollcommand=clients_scrollbar.set)
        clients_scrollbar.pack(side=tk.LEFT, fill=tk.Y)

        # --- Data Sending Frame ---
        data_sending_frame = ttk.LabelFrame(client_comm_frame, text="Send Data to Clients", padding="10")
        data_sending_frame.pack(fill=tk.X, pady=5)

        ttk.Label(data_sending_frame, text="Message:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.message_to_send_var = tk.StringVar()
        self.message_entry = ttk.Entry(data_sending_frame, textvariable=self.message_to_send_var, width=50)
        self.message_entry.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)

        self.send_to_selected_button = ttk.Button(data_sending_frame, text="Send to Selected", command=self.send_to_selected_client)
        self.send_to_selected_button.grid(row=0, column=2, padx=5, pady=2)

        self.broadcast_button = ttk.Button(data_sending_frame, text="Broadcast to All", command=self.broadcast_to_all_clients)
        self.broadcast_button.grid(row=0, column=3, padx=5, pady=2)
        
        data_sending_frame.columnconfigure(1, weight=1)

        self.client_listener_running = False
        self.server_ip_for_listener = None # Will be set by update_ip_in_gui

        master.protocol("WM_DELETE_WINDOW", self.on_exit)
        self.load_gui_config()

    def load_gui_config(self):
        self.config = load_config()
        self.iso_path_var.set(self.config.get("iso_path", ""))
        self.distro_var.set(self.config.get("distro_type", "Fedora"))
        self.net_iface_var.set(self.config.get("network_interface", ""))
        self.tftp_root_var.set(self.config.get("tftp_root", DEFAULT_CONFIG["tftp_root"]))
        self.http_root_var.set(self.config.get("http_root", DEFAULT_CONFIG["http_root"]))
        self.iso_mount_point_var.set(self.config.get("iso_mount_point", DEFAULT_CONFIG["iso_mount_point"]))

    def save_gui_config(self):
        self.config["iso_path"] = self.iso_path_var.get()
        self.config["distro_type"] = self.distro_var.get()
        self.config["network_interface"] = self.net_iface_var.get()
        self.config["tftp_root"] = self.tftp_root_var.get()
        self.config["http_root"] = self.http_root_var.get()
        self.config["iso_mount_point"] = self.iso_mount_point_var.get()
        save_config(self.config)

    def browse_iso(self):
        file_path = filedialog.askopenfilename(
            title="Select ISO File",
            filetypes=(("ISO files", "*.iso"), ("All files", "*.*"))
        )
        if file_path:
            self.iso_path_var.set(file_path)

    def update_ip_in_gui(self, ip_address, interface):
        self.config["server_ip"] = ip_address
        self.server_ip_for_listener = ip_address # Store for client listener
        log_message(f"GUI: Server IP updated to {ip_address} for interface {interface}")
        if not self.net_iface_var.get():
            self.net_iface_var.set(interface)

    def start_setup_thread(self):
        try:
            check_root_privileges()
        except PermissionError as e:
            messagebox.showerror("Permission Error", str(e))
            return

        self.save_gui_config()
        config_to_run = self.config.copy()

        self.start_button.config(state='disabled')
        self.save_button.config(state='disabled')

        setup_thread = threading.Thread(
            target=self._run_pxe_setup, 
            args=(config_to_run, self.update_ip_in_gui),
            daemon=True
        )
        setup_thread.start()

    def _run_pxe_setup(self, config_to_run, update_ip_callback_gui):
        try:
            log_message("GUI: Starting PXE setup process...")
            perform_pxe_setup(config_to_run, update_ip_callback=update_ip_callback_gui)
            log_message("GUI: PXE setup process completed from core.")
            # Attempt to auto-start client listener if IP is available
            if self.server_ip_for_listener and not self.client_listener_running:
                log_message(f"GUI: Auto-starting client listener on {self.server_ip_for_listener}.")
                self.master.after(0, self.toggle_client_listener_service, True) # True to indicate auto-start attempt
            elif not self.server_ip_for_listener:
                log_message("GUI: Server IP not available after PXE setup, cannot auto-start client listener.")
        except Exception as e:
            log_message(f"GUI: Error during PXE setup thread: {e}")
            messagebox.showerror("PXE Setup Error", f"An error occurred during PXE setup: {e}")
        finally:
            self.master.after(0, self._enable_buttons)

    def _enable_buttons(self):
        self.start_button.config(state='normal')
        self.save_button.config(state='normal')

    def _gui_new_client_connected(self, client_address):
        # This callback comes from a different thread, so use master.after for GUI updates
        self.master.after(0, self._update_client_listbox_add, client_address)

    def _update_client_listbox_add(self, client_address):
        client_str = f"{client_address[0]}:{client_address[1]}"
        self.connected_clients_listbox.insert(tk.END, client_str)
        log_message(f"GUI: Client connected - {client_str}")

    def _gui_client_disconnected(self, client_address):
        self.master.after(0, self._update_client_listbox_remove, client_address)

    def _update_client_listbox_remove(self, client_address):
        client_str = f"{client_address[0]}:{client_address[1]}"
        try:
            # Find and delete the client from the listbox
            list_items = self.connected_clients_listbox.get(0, tk.END)
            for i, item in enumerate(list_items):
                if item == client_str:
                    self.connected_clients_listbox.delete(i)
                    log_message(f"GUI: Client disconnected - {client_str}")
                    break
        except tk.TclError: # Catch error if listbox is already destroyed or item not found
            log_message(f"GUI: Error removing client {client_str} from listbox (may already be gone).")

    def _gui_data_received(self, client_address, data):
        log_message(f"GUI: Data from {client_address[0]}:{client_address[1]} - {data}")

    def toggle_client_listener_service(self, auto_start_attempt=False):
        if not self.client_listener_running:
            if not self.server_ip_for_listener:
                msg = "Server IP not set. Run PXE setup or configure network interface first."
                log_message(f"GUI: Cannot start client listener. {msg}")
                if not auto_start_attempt:
                    messagebox.showerror("Listener Error", msg)
                return

            log_message(f"GUI: Starting client listener on {self.server_ip_for_listener}:{CLIENT_LISTENER_PORT}")
            try:
                start_client_listener(
                    self.server_ip_for_listener, 
                    CLIENT_LISTENER_PORT, 
                    self._gui_new_client_connected, 
                    self._gui_data_received, 
                    self._gui_client_disconnected
                )
                self.client_listener_running = True
                self.toggle_listener_button.config(text="Stop Client Listener")
                self.client_listener_status_var.set(f"Listener: Running on {self.server_ip_for_listener}:{CLIENT_LISTENER_PORT}")
                log_message("GUI: Client listener started successfully.")
            except Exception as e:
                log_message(f"GUI: Failed to start client listener: {e}")
                messagebox.showerror("Listener Error", f"Failed to start client listener: {e}")
        else:
            log_message("GUI: Stopping client listener...")
            stop_client_listener()
            self.client_listener_running = False
            self.toggle_listener_button.config(text="Start Client Listener")
            self.client_listener_status_var.set("Listener: Stopped")
            self.connected_clients_listbox.delete(0, tk.END) # Clear client list
            log_message("GUI: Client listener stopped.")

    def send_to_selected_client(self):
        selected_indices = self.connected_clients_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("Send Error", "No client selected.")
            return
        
        selected_client_str = self.connected_clients_listbox.get(selected_indices[0])
        message = self.message_to_send_var.get()
        if not message:
            messagebox.showwarning("Send Error", "Message cannot be empty.")
            return

        # The listbox stores 'ip:port', core_connected_clients uses (ip, port) tuple as key
        # We need to find the correct key. This is a bit inefficient but direct.
        target_address_tuple = None
        for addr_tuple in core_connected_clients.keys(): # Accessing the global from nexus_core
            if f"{addr_tuple[0]}:{addr_tuple[1]}" == selected_client_str:
                target_address_tuple = addr_tuple
                break
        
        if target_address_tuple:
            log_message(f"GUI: Sending to {selected_client_str}: {message}")
            if send_data_to_client(target_address_tuple, message):
                log_message(f"GUI: Message sent successfully to {selected_client_str}.")
                self.message_to_send_var.set("") # Clear message field
            else:
                messagebox.showerror("Send Error", f"Failed to send message to {selected_client_str}.")
        else:
            messagebox.showerror("Send Error", f"Could not find client {selected_client_str} in core list. It might have disconnected.")

    def broadcast_to_all_clients(self):
        message = self.message_to_send_var.get()
        if not message:
            messagebox.showwarning("Broadcast Error", "Message cannot be empty.")
            return

        log_message(f"GUI: Broadcasting to all clients: {message}")
        sent_count, failed_clients = broadcast_data_to_all_clients(message)
        log_message(f"GUI: Broadcast attempt: {sent_count} successful, {len(failed_clients)} failed.")
        if failed_clients:
            messagebox.showwarning("Broadcast Info", f"Message broadcasted. Sent to {sent_count} clients. Failed for {len(failed_clients)}.")
        else:
            log_message("GUI: Message broadcasted successfully to all clients.")
        self.message_to_send_var.set("") # Clear message field

    def on_exit(self):
        if self.client_listener_running:
            log_message("GUI: Stopping client listener on exit...")
            stop_client_listener()
            self.client_listener_running = False # Ensure state is updated

        if messagebox.askokcancel("Exit", "Do you want to save your configuration before exiting?"):
            self.save_gui_config()
        self.master.destroy()
