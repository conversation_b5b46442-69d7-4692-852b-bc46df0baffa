#!/usr/bin/env python3

import sys
from nexus_api import app as flask_app # Import the Flask app instance
from nexus_utils import log_message, check_root_privileges # For logging and initial checks
import logging # For setting log level for messages from this file

# Note: init_logging is called at the beginning of nexus_api.py,
# so logs from here should also be captured by the queue if this script runs after nexus_api is imported.

if __name__ == "__main__":
    # Perform any pre-flight checks if necessary.
    # Root privileges check for starting the server on protected ports (e.g. 80, 443)
    # might be relevant, but for 5000 it's usually not needed unless specific FS access requires it.
    # For now, we assume the API endpoints themselves will handle permission checks where critical.
    # check_root_privileges() # This was for GUI/core functions, may not be needed to just start Flask on 5000

    log_message("Starting Nexus application server from nexus.py...", level=logging.INFO)

    # The Flask app is configured in nexus_api.py.
    # The logging is also initialized in nexus_api.py.
    # We just run the app here.
    # Default port is 5000, debug is True, host is '0.0.0.0' as set in nexus_api.py's original main block.
    # We can override here if needed, but it's better to keep config in one place.
    # The app.run() in nexus_api.py will be removed in the next step.
    # For now, if nexus_api.py's main block is still there, running this will start a second server if not careful.
    # This will be resolved by removing the main block from nexus_api.py.

    # To ensure nexus_api.py's app.run is not called if it's still present:
    # We directly call flask_app.run() here.

    try:
        # Host and port are now taken from nexus_api.py's app.run if __name__ == '__main__' was there.
        # Since we are removing that, we need to specify them here.
        # The previous app.run in nexus_api.py used: debug=True, host='0.0.0.0', port=5000
        log_message(f"Attempting to start Flask server on host 0.0.0.0, port 5000.", level=logging.INFO)
        flask_app.run(debug=True, host='0.0.0.0', port=5000)
    except Exception as e:
        log_message(f"Failed to start the Flask application: {e}", level=logging.CRITICAL)
        sys.exit(1)
