{% extends "base.html" %}

{% block content %}
<h2>Client Management</h2>

<div class="section">
    <h3>Client Listener Control</h3>
    <div id="listenerStatus">
        <p>Status: <span id="listenerState">Loading...</span></p>
        <p>IP Address: <span id="listenerIp">N/A</span></p>
        <p>Port: <span id="listenerPort">N/A</span></p>
    </div>
    <button id="startListenerButton">Start Listener</button>
    <button id="stopListenerButton">Stop Listener</button>
    <div id="listenerActionStatus" class="status-message" style="display:none; margin-top: 0.5rem;"></div>
</div>

<div class="section">
    <h3>Connected Clients</h3>
    <p>Clients will appear here when they connect to the listener.</p>
    <ul id="connectedClientsList">
        <!-- Client items will be added here by JavaScript -->
    </ul>
    <div id="clientsListStatus" class="status-message" style="display:none; margin-top: 0.5rem;"></div>
</div>

<div class="section">
    <h3>Client Messaging</h3>
    <label for="messageBroadcast">Message:</label>
    <textarea id="messageBroadcast" rows="3" style="width: 100%; box-sizing: border-box;"></textarea>
    <button id="broadcastMessageButton" style="margin-top: 0.5rem;">Broadcast to All Clients</button>
    <div id="messagingStatus" class="status-message" style="display:none; margin-top: 0.5rem;"></div>
</div>



{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Listener Control Elements
    const listenerStateSpan = document.getElementById('listenerState');
    const listenerIpSpan = document.getElementById('listenerIp');
    const listenerPortSpan = document.getElementById('listenerPort');
    const startListenerButton = document.getElementById('startListenerButton');
    const stopListenerButton = document.getElementById('stopListenerButton');
    const listenerActionStatusDiv = document.getElementById('listenerActionStatus');

    // Connected Clients Elements
    const connectedClientsListUl = document.getElementById('connectedClientsList');
    const clientsListStatusDiv = document.getElementById('clientsListStatus');

    // Messaging Elements
    const messageBroadcastTextarea = document.getElementById('messageBroadcast');
    const broadcastMessageButton = document.getElementById('broadcastMessageButton');
    const messagingStatusDiv = document.getElementById('messagingStatus');

    // --- Listener Control ---
    function fetchListenerStatus() {
        fetch('/api/listener/status')
            .then(response => response.json())
            .then(data => {
                if (data) {
                    listenerStateSpan.textContent = data.running ? 'Running' : 'Stopped';
                    listenerIpSpan.textContent = data.ip || 'N/A';
                    listenerPortSpan.textContent = data.port || 'N/A';
                    startListenerButton.disabled = data.running;
                    stopListenerButton.disabled = !data.running;
                }
            })
            .catch(error => {
                console.error('Error fetching listener status:', error);
                listenerStateSpan.textContent = 'Error';
                showListenerActionStatus('Could not fetch listener status.', 'error');
            });
    }

    startListenerButton.addEventListener('click', function() {
        showListenerActionStatus('Starting listener...', 'info');
        fetch('/api/listener/start', { method: 'POST' })
            .then(response => response.json().then(data => ({ status: response.status, body: data })))
            .then(({ status, body }) => {
                if (status === 200 || status === 201) { // 201 if newly created
                    showListenerActionStatus(body.message || 'Listener started.', 'success');
                    fetchListenerStatus(); // Refresh status
                } else {
                    showListenerActionStatus(`Error: ${body.error || 'Unknown error'}`, 'error');
                }
            })
            .catch(error => {
                 console.error('Error starting listener:', error);
                 showListenerActionStatus('Error starting listener.', 'error');
            });
    });

    stopListenerButton.addEventListener('click', function() {
        showListenerActionStatus('Stopping listener...', 'info');
        fetch('/api/listener/stop', { method: 'POST' })
            .then(response => response.json().then(data => ({ status: response.status, body: data })))
            .then(({ status, body }) => {
                if (status === 200) {
                    showListenerActionStatus(body.message || 'Listener stopped.', 'success');
                    fetchListenerStatus(); // Refresh status
                } else {
                    showListenerActionStatus(`Error: ${body.error || 'Unknown error'}`, 'error');
                }
            })
            .catch(error => {
                console.error('Error stopping listener:', error);
                showListenerActionStatus('Error stopping listener.', 'error');
            });
    });

    function showListenerActionStatus(message, type) {
        listenerActionStatusDiv.textContent = message;
        listenerActionStatusDiv.className = `status-message ${type}`;
        listenerActionStatusDiv.style.display = 'block';
        setTimeout(() => { listenerActionStatusDiv.style.display = 'none'; }, 5000);
    }

    // --- Connected Clients ---
    function fetchConnectedClients() {
        fetch('/api/clients')
            .then(response => response.json())
            .then(data => {
                connectedClientsListUl.innerHTML = ''; // Clear current list
                if (data && data.length > 0) {
                    clientsListStatusDiv.style.display = 'none';
                    data.forEach(client => {
                        const li = document.createElement('li');
                        li.textContent = `IP: ${client.ip}, Port: ${client.port}, Connected: ${new Date(client.connect_time).toLocaleString()}`;
                        connectedClientsListUl.appendChild(li);
                    });
                } else {
                    clientsListStatusDiv.textContent = 'No clients currently connected.';
                    clientsListStatusDiv.className = 'status-message info';
                    clientsListStatusDiv.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error fetching connected clients:', error);
                clientsListStatusDiv.textContent = 'Could not fetch client list.';
                clientsListStatusDiv.className = 'status-message error';
                clientsListStatusDiv.style.display = 'block';
            });
    }

    // --- Messaging ---
    broadcastMessageButton.addEventListener('click', function() {
        const message = messageBroadcastTextarea.value.trim();
        if (!message) {
            showMessagingStatus('Message cannot be empty.', 'error');
            return;
        }
        showMessagingStatus('Broadcasting message...', 'info');
        fetch('/api/clients/broadcast_message', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ message: message })
        })
        .then(response => response.json().then(data => ({ status: response.status, body: data })))
        .then(({ status, body }) => {
            if (status === 200) {
                showMessagingStatus(`Broadcast initiated to ${body.attempted_clients || 'N/A'} clients.`, 'success');
                messageBroadcastTextarea.value = ''; // Clear textarea
            } else {
                showMessagingStatus(`Error: ${body.error || 'Unknown error'}`, 'error');
            }
        })
        .catch(error => {
            console.error('Error broadcasting message:', error);
            showMessagingStatus('Error broadcasting message.', 'error');
        });
    });

    function showMessagingStatus(message, type) {
        messagingStatusDiv.textContent = message;
        messagingStatusDiv.className = `status-message ${type}`;
        messagingStatusDiv.style.display = 'block';
        setTimeout(() => { messagingStatusDiv.style.display = 'none'; }, 5000);
    }

    // Initial data fetch
    fetchListenerStatus();
    fetchConnectedClients();

    // Poll for updates
    setInterval(fetchListenerStatus, 10000); // Listener status every 10 seconds
    setInterval(fetchConnectedClients, 5000); // Client list every 5 seconds
});
</script>
{% endblock %}
