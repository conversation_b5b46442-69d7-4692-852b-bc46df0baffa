{% extends "base.html" %}

{% block content %}
<h2>System Configuration</h2>
<form id="configForm">
    <label for="iso_path">ISO Path:</label>
    <input type="text" id="iso_path" name="iso_path">

    <label for="distro_type">Distribution Type:</label>
    <select id="distro_type" name="distro_type">
        <option value="debian">Debian/Ubuntu</option>
        <option value="linux_mint">Linux Mint</option>
        <option value="rhel">RHEL/CentOS/Rocky</option>
        <!-- Add other options as necessary -->
    </select>

    <label for="network_interface">Network Interface (for PXE/Server IP):</label>
    <input type="text" id="network_interface" name="network_interface" placeholder="e.g., eth0">

    <label for="server_ip">Server IP (optional, auto-detected if interface is set):</label>
    <input type="text" id="server_ip" name="server_ip" placeholder="e.g., *************">
    <small class="form-text text-muted" style="display: block; margin-top: -0.25rem; margin-bottom: 0.5rem;">
        This must be an IP address assigned to an active network interface on the machine running the Nexus server (e.g., the IP of your eth0 or wlan0). The client listener will use this IP. If `netifaces` is unavailable and this field is left blank, PXE setup might also fail if it cannot determine an IP for the selected Network Interface.
    </small>

    <label for="tftp_root">TFTP Root Directory:</label>
    <input type="text" id="tftp_root" name="tftp_root" value="/srv/tftp">

    <label for="http_root">HTTP Root Directory (for Kickstart/Preseed):</label>
    <input type="text" id="http_root" name="http_root" value="/var/www/html/nexus">

    <label for="iso_mount_point">ISO Mount Point:</label>
    <input type="text" id="iso_mount_point" name="iso_mount_point" value="/mnt/nexus_iso">

    <button type="submit">Save Configuration</button>
</form>
<div id="statusMessage" class="status-message" style="display:none;"></div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('configForm');
        const statusMessageDiv = document.getElementById('statusMessage');

        // Fetch current config and populate form
        fetch('/api/config')
            .then(response => response.json())
            .then(data => {
                if (data) {
                    for (const key in data) {
                        if (form.elements[key]) {
                            form.elements[key].value = data[key];
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching config:', error);
                showStatus('Error fetching current configuration.', 'error');
            });

        // Handle form submission
        form.addEventListener('submit', function(event) {
            event.preventDefault();
            statusMessageDiv.style.display = 'none';

            const formData = new FormData(form);
            const configData = {};
            formData.forEach((value, key) => {
                configData[key] = value;
            });

            fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(configData),
            })
            .then(response => response.json().then(data => ({ status: response.status, body: data })))
            .then(({ status, body }) => {
                if (status === 200) {
                    showStatus('Configuration saved successfully!', 'success');
                    // Optionally re-populate form if response contains the full updated config
                    if (body) {
                         for (const key in body) {
                            if (form.elements[key]) {
                                form.elements[key].value = body[key];
                            }
                        }
                    }
                } else {
                    showStatus(`Error saving configuration: ${body.error || 'Unknown error'}`, 'error');
                }
            })
            .catch(error => {
                console.error('Error saving config:', error);
                showStatus('Error saving configuration.', 'error');
            });
        });

        function showStatus(message, type) {
            statusMessageDiv.textContent = message;
            statusMessageDiv.className = `status-message ${type}`;
            statusMessageDiv.style.display = 'block';
        }
    });
</script>
{% endblock %}
