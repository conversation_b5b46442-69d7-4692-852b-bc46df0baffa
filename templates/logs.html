{% extends "base.html" %}

{% block content %}
<h2>System Logs</h2>
<div id="logContainer" class="log-container">
    Loading logs...
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const logContainer = document.getElementById('logContainer');

        function fetchLogs() {
            fetch('/api/logs')
                .then(response => response.json())
                .then(data => {
                    if (data && data.length > 0) {
                        // If first time or "Loading logs..." is present, clear it
                        if (logContainer.textContent.trim() === "Loading logs..." || logContainer.innerHTML === "Loading logs...") {
                            logContainer.innerHTML = '';
                        }
                        data.forEach(logEntry => {
                            const logElement = document.createElement('div');
                            logElement.textContent = logEntry;
                            logContainer.appendChild(logElement);
                        });
                        // Scroll to the bottom
                        logContainer.scrollTop = logContainer.scrollHeight;
                    } else if (logContainer.textContent.trim() === "Loading logs..." || logContainer.innerHTML === "Loading logs...") {
                        // No new logs, and still showing "Loading logs..."
                        // Can leave it or change to "No logs available."
                        // For now, leave as is, new logs might appear.
                    }
                })
                .catch(error => {
                    console.error('Error fetching logs:', error);
                    const errorElement = document.createElement('div');
                    errorElement.textContent = "Error fetching logs. Check console.";
                    errorElement.style.color = 'red';
                    logContainer.appendChild(errorElement);
                });
        }

        // Fetch logs initially
        fetchLogs();

        // Optionally, refresh logs every few seconds
        setInterval(fetchLogs, 5000); // Refresh every 5 seconds
    });
</script>
{% endblock %}
