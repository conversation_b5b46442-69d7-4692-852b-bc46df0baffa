{% extends "base.html" %}

{% block content %}
<h2>PXE Control</h2>
<p>This section allows you to initiate the PXE (Preboot Execution Environment) setup process on the server. This process configures services like TFTP and DHCP (if applicable) to allow network booting of client machines.</p>

<button id="startPxeButton">Start PXE Setup</button>

<div id="pxeStatusMessage" class="status-message" style="display:none; margin-top: 1rem;"></div>

<h3>Notes:</h3>
<ul>
    <li>Ensure that the system configuration (especially Network Interface and ISO path) is correctly set before starting.</li>
    <li>This process may require root privileges. The API will attempt to run necessary commands using sudo if configured, or will fail if privileges are insufficient.</li>
    <li>Monitor the <a href="{{ url_for('show_logs') }}">system logs</a> for detailed progress and any errors during the setup.</li>
</ul>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startPxeButton = document.getElementById('startPxeButton');
    const pxeStatusMessageDiv = document.getElementById('pxeStatusMessage');

    startPxeButton.addEventListener('click', function() {
        pxeStatusMessageDiv.style.display = 'none';
        pxeStatusMessageDiv.textContent = '';
        pxeStatusMessageDiv.className = 'status-message'; // Reset class

        // Add a loading indicator
        showPxeStatus('Initiating PXE setup... This may take a moment.', 'info');


        fetch('/api/pxe/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                // Include other headers like authorization if ever needed
            }
        })
        .then(response => response.json().then(data => ({ status: response.status, body: data })))
        .then(({ status, body }) => {
            if (status === 200) {
                showPxeStatus(`PXE setup process initiated: ${body.message}`, 'success');
            } else {
                showPxeStatus(`Error starting PXE setup: ${body.error || 'Unknown error'} (Status: ${status})`, 'error');
            }
        })
        .catch(error => {
            console.error('Error starting PXE setup:', error);
            showPxeStatus('An unexpected error occurred while trying to start PXE setup. Check browser console for details.', 'error');
        });
    });

    function showPxeStatus(message, type) {
        pxeStatusMessageDiv.textContent = message;
        pxeStatusMessageDiv.className = `status-message ${type}`; // type will be 'success', 'error', or 'info'
        pxeStatusMessageDiv.style.display = 'block';
    }
});
</script>
{% endblock %}
