import os
import json
from nexus_utils import log_message

CONFIG_FILE = "config.json"
DEFAULT_CONFIG = {
    "iso_path": "",
    "distro_type": "Fedora", # Added for multi-distro support
    "network_interface": "",  # Empty means auto-detect
    "server_ip": "", # Will be auto-detected and stored for reference
    "tftp_root": "/var/lib/tftpboot",
    "http_root": "/var/www/html",
    "iso_mount_point": "/mnt/iso_nexus_temp"
}

def load_config():
    """Loads configuration from JSON file."""
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r') as f:
                config = json.load(f)
                for key, value in DEFAULT_CONFIG.items():
                    if key not in config:
                        config[key] = value
                return config
        except json.JSONDecodeError:
            log_message(f"Error: {CONFIG_FILE} is corrupted. Using default configuration.")
            return DEFAULT_CONFIG.copy()
        except Exception as e:
            log_message(f"Error loading {CONFIG_FILE}: {e}. Using default configuration.")
            return DEFAULT_CONFIG.copy()
    return DEFAULT_CONFIG.copy()

def save_config(config):
    """Saves configuration to JSON file."""
    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=4)
        log_message(f"Configuration saved to {CONFIG_FILE}")
    except Exception as e:
        log_message(f"Error saving configuration: {e}")
