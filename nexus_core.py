import os
import subprocess
try:
    import netifaces
    NETIFACES_AVAILABLE = True
except ImportError:
    NETIFACES_AVAILABLE = False
    # log_message is not available yet at top level.
    # This status can be logged when detect_network_info is called.

import socket
import threading
from nexus_utils import log_message, run_command, backup_file, check_root_privileges

# --- Core Logic Functions ---

def validate_input_config(current_config):
    log_message("Step 1: Validating input...")
    iso_path = current_config.get("iso_path")
    if not iso_path or not os.path.isfile(iso_path):
        msg = f"Error: ISO file not found or not specified: {iso_path}"
        log_message(msg)
        raise ValueError(msg)
    if not iso_path.endswith(".iso"):
        log_message(f"Warning: Provided ISO path '{iso_path}' does not end with .iso. Proceeding anyway.")
    
    log_message(f"  ISO Path: {iso_path}")
    log_message(f"  Distro: {current_config['distro_type']}")
    log_message(f"  TFTP Root: {current_config['tftp_root']}")
    log_message(f"  HTTP Root: {current_config['http_root']}")
    log_message(f"  Network Interface: {current_config.get('network_interface') or 'Auto-detect'}")
    log_message("Input validation successful.")
    return True

def mount_iso(iso_path, mount_point):
    log_message(f"Step 2: Mounting ISO {iso_path} to {mount_point}...")
    run_command(["sudo", "mkdir", "-p", mount_point])
    run_command(["sudo", "umount", mount_point], check=False, capture_output=True)
    run_command(["sudo", "mount", "-o", "loop", iso_path, mount_point])
    log_message(f"  ISO mounted successfully at {mount_point}.")

def install_dependencies():
    log_message("Step 3: Installing dependencies (dnsmasq, httpd, syslinux)...")
    packages = ["dnsmasq", "httpd", "syslinux"]
    run_command(["sudo", "dnf", "install", "-y"] + packages)
    log_message("Dependencies installed/verified.")

def detect_network_info(user_interface=None, current_config=None):
    log_message("Step 4: Detecting network information...")
    if not NETIFACES_AVAILABLE:
        log_message("  Warning: 'netifaces' module is not available.")
        if user_interface and current_config and current_config.get("server_ip"):
            server_ip = current_config.get("server_ip")
            log_message(f"  Using user-specified interface '{user_interface}' and pre-configured server_ip '{server_ip}' due to missing netifaces.")
            # Basic validation for server_ip format could be added here if desired
            return user_interface, server_ip
        elif user_interface:
            err_msg = ("Error: 'netifaces' module is unavailable, and 'server_ip' was not pre-configured. "
                       "Cannot determine IP address for the specified interface. "
                       "Please install 'netifaces' or provide both 'network_interface' and 'server_ip' in the configuration.")
            log_message(err_msg)
            raise ConnectionError(err_msg)
        else:
            err_msg = ("Error: 'netifaces' module is unavailable. "
                       "Cannot auto-detect network interface or IP address. "
                       "Please install 'netifaces' or provide 'network_interface' and 'server_ip' in the configuration.")
            log_message(err_msg)
            raise ConnectionError(err_msg)

    # Proceed with netifaces if available
    if user_interface:
        log_message(f"  User specified interface: {user_interface}")
        try:
            addrs = netifaces.ifaddresses(user_interface)
            ip_info = addrs[netifaces.AF_INET][0] # TODO: Check netifaces.AF_INET key exists
            ip_address = ip_info['addr']
            log_message(f"  Using interface: {user_interface}, IP Address: {ip_address}")
            return user_interface, ip_address
        except (ValueError, KeyError, IndexError) as e: # Added IndexError
            err_msg = f"Error: Could not get IP address for specified interface '{user_interface}'. Ensure it's correct, active and has an IPv4 address. Details: {e}"
            log_message(err_msg)
            raise ValueError(err_msg)

    interfaces = netifaces.interfaces()
    log_message(f"  Available interfaces: {interfaces}")
    
    default_route_interface = None
    try:
        gws = netifaces.gateways()
        if 'default' in gws and netifaces.AF_INET in gws['default']:
            default_route_interface = gws['default'][netifaces.AF_INET][1]
            log_message(f"  Default route interface: {default_route_interface}")
    except Exception as e:
        log_message(f"  Could not determine default route interface via netifaces.gateways(): {e}. Will try common interfaces.")

    priority_interfaces = [i for i in interfaces if i.startswith(('eth', 'enp', 'eno'))]
    priority_interfaces.extend([i for i in interfaces if i.startswith(('wlan', 'wlp'))])
    priority_interfaces.extend([i for i in interfaces if i not in priority_interfaces and i != 'lo'])

    if default_route_interface and default_route_interface in interfaces:
        try:
            addrs = netifaces.ifaddresses(default_route_interface)
            if netifaces.AF_INET in addrs:
                ip_info = addrs[netifaces.AF_INET][0]
                ip_address = ip_info['addr']
                log_message(f"  Using default route interface: {default_route_interface}, IP Address: {ip_address}")
                return default_route_interface, ip_address
        except Exception as e:
            log_message(f"  Could not get IP for default route interface {default_route_interface}: {e}") 

    for iface_name in priority_interfaces:
        try:
            addrs = netifaces.ifaddresses(iface_name)
            if netifaces.AF_INET in addrs: # Check AF_INET key
                ip_info = addrs[netifaces.AF_INET][0]
                ip_address = ip_info['addr']
                if not ip_address.startswith("127.") and not ip_address.startswith("169.254."):
                    log_message(f"  Auto-detected active interface: {iface_name}, IP Address: {ip_address}")
                    return iface_name, ip_address
        except (KeyError, IndexError) as e: # Added IndexError
            log_message(f"  Skipping interface {iface_name} due to missing AF_INET or address info: {e}")
            continue
    
    err_msg = "Error: Could not auto-detect a suitable active network interface with an IP address using 'netifaces'."
    log_message(err_msg)
    raise ConnectionError(err_msg)

def configure_tftp(tftp_root, server_ip, iso_mount_point, distro_type, http_root):
    log_message(f"Step 5: Configuring TFTP server for {distro_type}...")
    run_command(["sudo", "mkdir", "-p", tftp_root])

    log_message("  Copying SYSLINUX boot files (pxelinux.0, ldlinux.c32, vesamenu.c32)...")
    run_command(["sudo", "cp", "/usr/share/syslinux/pxelinux.0", tftp_root])
    run_command(["sudo", "cp", "/usr/share/syslinux/ldlinux.c32", tftp_root])
    run_command(["sudo", "cp", "/usr/share/syslinux/vesamenu.c32", tftp_root])
    run_command(["sudo", "cp", "/usr/share/syslinux/libcom32.c32", tftp_root])
    run_command(["sudo", "cp", "/usr/share/syslinux/libutil.c32", tftp_root])
    
    if distro_type == "Fedora":
        vmlinuz_src = os.path.join(iso_mount_point, "isolinux/vmlinuz")
        initrd_src = os.path.join(iso_mount_point, "isolinux/initrd.img")
        log_message("  Copying Fedora kernel and initrd...")
    elif distro_type == "linux_mint":
        vmlinuz_src = os.path.join(iso_mount_point, "casper/vmlinuz")
        initrd_src = os.path.join(iso_mount_point, "casper/initrd.lz")
        log_message("  Copying Linux Mint kernel and initrd...")
    elif distro_type == "linux_mint":
        pass # Handled below
    else:
        raise ValueError(f"Unsupported distro_type for TFTP configuration: {distro_type}")

    run_command(["sudo", "cp", vmlinuz_src, os.path.join(tftp_root, "vmlinuz")])
    run_command(["sudo", "cp", initrd_src, os.path.join(tftp_root, "initrd.img")])

    pxelinux_cfg_dir = os.path.join(tftp_root, "pxelinux.cfg")
    run_command(["sudo", "mkdir", "-p", pxelinux_cfg_dir])

    pxe_config_file = os.path.join(pxelinux_cfg_dir, "default")
    backup_file(pxe_config_file)

    if distro_type == "Fedora":
        pxe_config_content = f"""
DEFAULT vesamenu.c32
PROMPT 0
TIMEOUT 300
ONTIMEOUT local

LABEL pxe_boot_fedora
  MENU LABEL Boot Fedora Live (PXE)
  KERNEL vmlinuz
  APPEND initrd=initrd.img root=live:http://{server_ip}/LiveOS/squashfs.img rd.live.image mitigations=off nexus_server_ip={server_ip} nexus_server_port={CLIENT_LISTENER_PORT}
"""
    elif distro_type == "linux_mint":
        # Note: A fully functional Mint PXE boot requires an NFS server to host the root filesystem.
        # This configuration is simplified and may not work without additional manual setup.
        log_message("  WARNING: The generated Linux Mint PXE config is simplified and requires a manual NFS setup to be fully functional.")
        pxe_config_content = f"""
DEFAULT pxe_boot_mint
PROMPT 0
TIMEOUT 10 # 1 second timeout for automatic boot

LABEL pxe_boot_mint
  MENU LABEL Boot Linux Mint Live (PXE) # This label is still useful for clarity
  KERNEL vmlinuz
  APPEND initrd=initrd.img boot=casper netboot=nfs nfsroot={server_ip}:{http_root} ip=dhcp ro quiet splash nexus_server_ip={server_ip} nexus_server_port={CLIENT_LISTENER_PORT} -- # Added quiet splash for a cleaner boot
"""

    log_message(f"  Generating PXE boot config: {pxe_config_file}")
    temp_pxe_config = "/tmp/pxelinux.cfg.default.nexus_temp"
    with open(temp_pxe_config, "w") as f:
        f.write(pxe_config_content)
    run_command(["sudo", "cp", temp_pxe_config, pxe_config_file])
    run_command(["sudo", "rm", temp_pxe_config])

    log_message("TFTP configuration complete.")

def configure_http(http_root, iso_mount_point, distro_type):
    log_message(f"Step 6: Configuring HTTP server for {distro_type}...")
    run_command(["sudo", "mkdir", "-p", http_root])
    
    if distro_type == "Fedora":
        http_live_dir = os.path.join(http_root, "LiveOS")
        iso_live_dir = os.path.join(iso_mount_point, "LiveOS")
        squashfs_img = os.path.join(iso_live_dir, "squashfs.img")
        
        log_message(f"  Creating HTTP directory: {http_live_dir}")
        run_command(["sudo", "mkdir", "-p", http_live_dir])
        
        log_message(f"  Copying squashfs.img to {http_live_dir}...")
        run_command(["sudo", "cp", squashfs_img, http_live_dir])

    elif distro_type == "linux_mint":
        log_message(f"  Copying entire ISO content to {http_root} for {distro_type} (this may take a while)...")
        run_command(f"sudo rsync -a --info=progress2 {iso_mount_point}/ {http_root}/", shell=True)

    log_message("HTTP configuration complete.")

def configure_dnsmasq(server_ip, tftp_root, network_interface):
    log_message("Step 7: Configuring dnsmasq...")
    config_file = "/etc/dnsmasq.conf"
    backup_file(config_file)

    ip_parts = server_ip.split('.')
    if len(ip_parts) != 4:
        err_msg = f"Error: Invalid server IP format: {server_ip}"
        log_message(err_msg)
        raise ValueError(err_msg)

    net_prefix = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}"
    dhcp_range_start = f"{net_prefix}.50"
    dhcp_range_end = f"{net_prefix}.150"
    dhcp_lease_time = "12h"

    dnsmasq_config_content = f"""
# Configuration generated by Nexus PXE Boot Server script
interface={network_interface}
bind-interfaces
listen-address={server_ip}
domain=nexus.local
dhcp-range={dhcp_range_start},{dhcp_range_end},{dhcp_lease_time}
dhcp-option=option:router,{server_ip}
dhcp-boot=pxelinux.0,{server_ip}
enable-tftp
tftp-root={tftp_root}
"""
    dnsmasq_conf_path = "/etc/dnsmasq.conf"
    log_message(f"  Writing dnsmasq configuration to {dnsmasq_conf_path}")
    temp_dnsmasq_conf = "/tmp/dnsmasq.conf.nexus_temp"
    try:
        with open(temp_dnsmasq_conf, "w") as f:
            f.write(dnsmasq_config_content)
        run_command(["sudo", "cp", temp_dnsmasq_conf, dnsmasq_conf_path])
        run_command(["sudo", "rm", temp_dnsmasq_conf])
    except Exception as e:
        log_message(f"Error writing dnsmasq config: {e}")
        raise
    log_message("dnsmasq configuration complete.")

def manage_firewall():
    log_message("Step 8: Managing firewall rules...")
    try:
        log_message("  Backing up firewall configuration...")
        zone_name_stdout, _ = run_command(
            ["sudo", "firewall-cmd", "--get-default-zone"],
            capture_output=True, text=True, check=True
        )
        zone_name = zone_name_stdout.strip()
        if zone_name:
            zone_file_path = f"/etc/firewalld/zones/{zone_name}.xml"
            backup_file(zone_file_path)
        else:
            log_message("  Warning: Could not determine default firewall zone. Skipping backup.")
    except Exception as e:
        log_message(f"  Warning: Failed to back up firewall configuration: {e}")

    services_to_add = ["tftp", "http", "dhcp"]
    for service in services_to_add:
        log_message(f"  Adding firewall rule for {service}...")
        run_command(["sudo", "firewall-cmd", "--add-service=" + service, "--permanent"], capture_output=True)
        run_command(["sudo", "firewall-cmd", "--add-service=" + service], capture_output=True)

    log_message("  Reloading firewall to apply permanent rules...")
    stdout, stderr = run_command(["sudo", "firewall-cmd", "--reload"], capture_output=True)

    if "success" in stdout.lower() or "success" in stderr.lower():
        log_message("  Firewall reloaded successfully.")
    else:
        log_message(f"  Warning: Firewall reload might not have been fully successful. Stdout: {stdout}, Stderr: {stderr}")

    log_message("Firewall rules updated.")

def start_services():
    log_message("Step 9: Starting services (dnsmasq, httpd)...")
    services = ["dnsmasq", "httpd"]
    for service in services:
        log_message(f"  Enabling and starting {service}...")
        run_command(["sudo", "systemctl", "enable", service])
        run_command(["sudo", "systemctl", "restart", service])
        status_stdout, _ = run_command(["sudo", "systemctl", "is-active", service], capture_output=True, check=False)
        if status_stdout.strip() == "active":
            log_message(f"  Service {service} is active.")
        else:
            log_message(f"  Warning: Service {service} may not have started correctly. Status: {status_stdout.strip()}")
            log_message(f"  Check service logs: sudo journalctl -u {service}")
    log_message("Services started.")

def cleanup(iso_mount_point):
    log_message("Step 10: Cleaning up...")
    log_message(f"  Unmounting ISO from {iso_mount_point}...")
    run_command(["sudo", "umount", iso_mount_point], check=False, capture_output=True)
    try:
        if os.path.exists(iso_mount_point) and not os.listdir(iso_mount_point):
            run_command(["sudo", "rmdir", iso_mount_point], check=False)
            log_message(f"  Removed temporary mount point {iso_mount_point}.")
    except OSError as e:
        log_message(f"  Warning: Could not remove mount point {iso_mount_point}: {e}")
    log_message("Cleanup complete.")

# --- Client Communication Server ---

CLIENT_LISTENER_PORT = 9999  # Default port, can be made configurable

client_listener_thread = None
client_server_socket = None
connected_clients = {}  # { address_tuple: client_socket }
clients_lock = threading.Lock()
listener_stop_event = threading.Event()

def _handle_client_connection(client_socket, client_address, new_client_cb_core, data_received_cb_core, client_disconnected_cb_core):
    log_message(f"Client comm: New connection from {client_address}")
    try:
        with clients_lock:
            connected_clients[client_address] = client_socket
        if new_client_cb_core: # Callback for GUI update or core logic
            new_client_cb_core(client_address)

        client_socket.settimeout(1.0) # Timeout for recv to allow checking stop_event
        while not listener_stop_event.is_set():
            try:
                data = client_socket.recv(4096)  # Adjust buffer size as needed
                if not data:
                    log_message(f"Client comm: Client {client_address} disconnected (no data).")
                    break # Connection closed by client
                
                decoded_data = data.decode('utf-8') # Assuming UTF-8 encoded data
                log_message(f"Client comm: Received from {client_address}: {decoded_data}")
                if data_received_cb_core:
                    data_received_cb_core(client_address, decoded_data)

            except socket.timeout:
                continue # Just loop again if timeout occurs, allows checking listener_stop_event
            except ConnectionResetError:
                log_message(f"Client comm: Connection reset by {client_address}.")
                break
            except Exception as e:
                log_message(f"Client comm: Error receiving data from {client_address}: {e}")
                break
    finally:
        with clients_lock:
            if client_address in connected_clients:
                del connected_clients[client_address]
        try:
            client_socket.close()
        except Exception as e:
            log_message(f"Client comm: Error closing socket for {client_address}: {e}")
        
        if client_disconnected_cb_core:
            client_disconnected_cb_core(client_address)
        log_message(f"Client comm: Connection with {client_address} closed.")

def _listener_accept_loop(host_ip, port, new_client_cb_core, data_received_cb_core, client_disconnected_cb_core):
    global client_server_socket
    try:
        client_server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        client_server_socket.bind((host_ip, port))
        client_server_socket.listen(5)
        client_server_socket.settimeout(1.0) # Timeout to allow checking stop_event
        log_message(f"Client comm: Listener started on {host_ip}:{port}")

        while not listener_stop_event.is_set():
            try:
                client_socket, client_address = client_server_socket.accept()
                # The new_client_cb_core is called from _handle_client_connection after adding to list
                
                handler_thread = threading.Thread(
                    target=_handle_client_connection, 
                    args=(client_socket, client_address, new_client_cb_core, data_received_cb_core, client_disconnected_cb_core),
                    daemon=True
                )
                handler_thread.start()
            except socket.timeout:
                continue # Allow checking listener_stop_event
            except Exception as e:
                if not listener_stop_event.is_set(): # Avoid logging error if we are stopping
                    log_message(f"Client comm: Error accepting connections: {e}")
                break

    except Exception as e:
        log_message(f"Client comm: Listener thread critical error: {e}")
    finally:
        if client_server_socket:
            try:
                client_server_socket.close()
            except Exception as e:
                log_message(f"Client comm: Error closing main server socket during cleanup: {e}")
            client_server_socket = None
        log_message("Client comm: Listener stopped.")

def start_client_listener(host_ip, port=CLIENT_LISTENER_PORT, new_client_cb=None, data_received_cb=None, client_disconnected_cb=None):
    global client_listener_thread
    if client_listener_thread and client_listener_thread.is_alive():
        log_message("Client comm: Listener already running.")
        return

    listener_stop_event.clear()
    client_listener_thread = threading.Thread(
        target=_listener_accept_loop, 
        args=(host_ip, port, new_client_cb, data_received_cb, client_disconnected_cb),
        daemon=True
    )
    client_listener_thread.start()
    log_message("Client comm: Starting listener service...")

def stop_client_listener():
    global client_listener_thread, client_server_socket, connected_clients
    log_message("Client comm: Stopping listener service...")
    listener_stop_event.set()

    # Close the main server socket to stop accepting new connections
    if client_server_socket:
        try:
            client_server_socket.close() 
        except Exception as e:
            log_message(f"Client comm: Error closing server socket: {e}")
        # client_server_socket = None # Let the accept loop handle this

    # Close all active client connections
    active_client_sockets_info = []
    with clients_lock:
        for addr, sock in list(connected_clients.items()): # list() for safe iteration
            active_client_sockets_info.append((sock, addr))
            # _handle_client_connection's finally block will remove from connected_clients
    
    for sock, addr in active_client_sockets_info:
        try:
            sock.shutdown(socket.SHUT_RDWR) # Signal client to close
            sock.close()
            log_message(f"Client comm: Forcibly closed connection with {addr}")
        except Exception as e:
            log_message(f"Client comm: Error shutting/closing client socket for {addr}: {e}")

    if client_listener_thread and client_listener_thread.is_alive():
        client_listener_thread.join(timeout=5.0)
        if client_listener_thread.is_alive():
            log_message("Client comm: Listener thread did not terminate cleanly.")
    
    client_listener_thread = None
    with clients_lock:
        connected_clients.clear() # Ensure list is clear after threads should have exited
    log_message("Client comm: Listener service stopped and clients cleared.")

def send_data_to_client(client_address_tuple, data_payload):
    with clients_lock:
        client_socket = connected_clients.get(client_address_tuple)
    
    if client_socket:
        try:
            client_socket.sendall(str(data_payload).encode('utf-8'))
            log_message(f"Client comm: Sent to {client_address_tuple}: {data_payload}")
            return True
        except Exception as e:
            log_message(f"Client comm: Error sending data to {client_address_tuple}: {e}")
            # Consider removing client or flagging as problematic
            return False
    else:
        log_message(f"Client comm: Client {client_address_tuple} not found for sending data.")
        return False

def broadcast_data_to_all_clients(data_payload):
    sent_count = 0
    failed_clients = []
    with clients_lock:
        # Iterate over a copy of items in case of modification during iteration elsewhere
        for client_address_tuple, client_socket in list(connected_clients.items()):
            try:
                client_socket.sendall(str(data_payload).encode('utf-8'))
                sent_count += 1
            except Exception as e:
                log_message(f"Client comm: Error broadcasting to {client_address_tuple}: {e}")
                failed_clients.append(client_address_tuple)
    
    log_message(f"Client comm: Broadcast sent to {sent_count} clients. Failed for {len(failed_clients)}.")
    return sent_count, failed_clients


# Orchestrates the PXE server setup process.
def perform_pxe_setup(current_config, update_ip_callback=None):
    """Orchestrates the PXE server setup process."""
    try:
        check_root_privileges()
        validate_input_config(current_config)
        mount_iso(current_config["iso_path"], current_config["iso_mount_point"])
        
        install_dependencies()
        
        user_selected_interface = current_config.get("network_interface")
        # Pass current_config to detect_network_info for the NETIFACES_AVAILABLE case
        network_interface, server_ip = detect_network_info(user_selected_interface, current_config)
        
        current_config["server_ip"] = server_ip # Update config with detected/validated IP
        if not user_selected_interface and NETIFACES_AVAILABLE: # Only update interface if auto-detected
            current_config["network_interface"] = network_interface
        
        if update_ip_callback:
            update_ip_callback(server_ip, network_interface)
            
        log_message(f"  Server will operate on IP: {server_ip} via interface: {network_interface}")
        if network_interface.startswith("wl"):
            log_message("  WARNING: PXE booting over Wi-Fi is selected. This can be unreliable.")

        distro_type = current_config.get("distro_type", "Fedora")
        configure_tftp(current_config["tftp_root"], server_ip, current_config["iso_mount_point"], distro_type, current_config["http_root"])
        configure_http(current_config["http_root"], current_config["iso_mount_point"], distro_type)
        configure_dnsmasq(server_ip, current_config["tftp_root"], network_interface)
        manage_firewall()
        start_services()
        
        log_message("\n--- PXE Server Setup Complete! ---")
        log_message(f"Clients on the {network_interface} network can now boot from this server.")

    except (ValueError, PermissionError, ConnectionError, subprocess.CalledProcessError) as e:
        log_message(f"\n--- SETUP FAILED ---")
        log_message(f"An error occurred: {e}")
        raise
    except Exception as e:
        log_message(f"\n--- SETUP FAILED ---")
        log_message(f"An unexpected error occurred: {e}")
        import traceback
        log_message(traceback.format_exc())
        raise
    finally:
        cleanup(current_config["iso_mount_point"])
