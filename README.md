# Nexus PXE Server Manager (Web UI)

Nexus is a Python application with a web-based user interface designed to simplify the setup and management of a PXE (Preboot Execution Environment) boot server. It helps automate the configuration of services like DHCP, TFTP, and HTTP needed for network booting, initially focused on Fedora Live ISOs but configurable for other distributions.

## Goal

Provide a user-friendly web interface to configure and manage a PXE boot server, making it easier to deploy operating systems or live environments to client machines on the same network.

## Prerequisites

-   Python 3.x
-   A compatible Linux distribution for the server (initially developed on Fedora).
-   Required Python packages (see `requirements.txt`). Key among them is `Flask`.
-   An OS ISO file (e.g., Fedora Workstation Live ISO).
-   `sudo` privileges are required for the user running Nexus if they intend to use the PXE Setup feature, as it typically involves managing system services and network configurations.
-   The `netifaces` Python package is recommended for automatic detection of network interface IP addresses. If not installed, the network interface and server IP may need to be manually specified in the configuration for some features.

## Setup

1.  Clone this repository:
    ```bash
    git clone <repository_url>
    cd nexus-pxe-server
    ```
2.  Install required Python packages:
    ```bash
    pip install -r requirements.txt
    ```

## Running the Application

1.  Navigate to the application directory.
2.  Run the main script:
    ```bash
    python nexus.py
    ```
3.  The application will start a web server, typically on `http://0.0.0.0:5000`.
4.  Open your web browser and go to `http://localhost:5000` (or the IP of the server machine if accessing remotely) to use the Nexus Web UI.

## Web UI Features

The Nexus Web UI provides the following sections:

-   **Configuration**:
    -   Set paths for your ISO file, TFTP root, HTTP root, and ISO mount point.
    -   Choose the distribution type (e.g., Fedora, Mint - this may affect PXE settings).
    -   Specify the network interface for the PXE services and the Server IP for the client listener.
    -   Save your configuration.
-   **PXE Control**:
    -   Start the PXE setup process. This will use the saved configuration to set up and start the necessary PXE services (DHCP, TFTP, HTTP).
    -   Requires the application to be run with sufficient privileges (e.g., `sudo python nexus.py`) if not already root, as it modifies system services.
-   **Client Management**:
    -   Start or stop the client listener service, which allows Nexus to communicate with connected PXE clients.
    -   View a list of currently connected clients.
    -   Send broadcast messages to all connected clients.
-   **View Logs**:
    -   See real-time log messages from the application, including PXE setup progress, client connections, and errors.

## Key Configuration and Operational Notes

-   **Root Privileges for PXE Setup**: The "Start PXE Setup" feature requires root/sudo privileges because it manages system services (like `dnsmasq`, `httpd`) and network configurations. The application will indicate if privileges are insufficient when you attempt this action. You might need to run `sudo python nexus.py`.
-   **Server IP for Client Listener**: In the Configuration page, the "Server IP" field for the client listener must be an IP address that is actively assigned to a network interface on the machine running the Nexus server. The listener will attempt to bind to this IP. If this IP is incorrect or unavailable, the listener may not start correctly (check logs for details).
-   **Network Interface and `netifaces`**:
    -   If the `netifaces` Python package is not installed, Nexus cannot automatically detect IP addresses for network interfaces.
    *   In this case, you **must** manually specify both the "Network Interface" (e.g., `eth0`) and the "Server IP" in the Configuration for the PXE setup to work correctly, as it needs to determine the IP for the selected interface.
-   **Firewall**: Ensure that your server's firewall allows traffic for DHCP (UDP port 67), TFTP (UDP port 69), HTTP (TCP port 80, or as configured), and the client listener port (TCP 6969 by default).

## Technical Details (Core PXE Process)

The underlying PXE boot process typically involves:
-   **DHCP**: `dnsmasq` (or a similar service configured by Nexus) assigns IP addresses to clients and informs them of the TFTP server's address and boot filename.
-   **TFTP**: The TFTP server (often `dnsmasq`) serves the initial bootloader (`pxelinux.0`), kernel, and initramfs images.
-   **HTTP**: For larger files like the main OS image (`squashfs.img` in many live systems), an HTTP server is used to serve these to the client after the kernel boots.
-   **PXE Boot Menu**: `pxelinux.0` often loads a configuration file from `pxelinux.cfg/default` (or similar) which defines the boot options.

Nexus helps automate the setup of these components based on your configuration.

## Disclaimer

This application modifies system configurations and installs/manages software services. Use with caution and ensure you understand the changes being made. It's recommended to use this in a test environment or on a dedicated machine first.
