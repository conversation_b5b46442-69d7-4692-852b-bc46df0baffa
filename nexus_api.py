import queue
import logging
from flask import Flask, jsonify, request, render_template # Added render_template
from nexus_config import load_config, save_config
from nexus_core import perform_pxe_setup, start_client_listener, stop_client_listener, CLIENT_LISTENER_PORT, send_data_to_client, broadcast_data_to_all_clients
from nexus_utils import check_root_privileges, init_logging, log_message
import threading
import datetime

# Initialize logging for the API
api_log_queue = queue.Queue()
init_logging(log_queue_api=api_log_queue)

app = Flask(__name__) # Flask will automatically look for 'templates' and 'static' folders.

# --- HTML Serving Routes ---
@app.route('/', methods=['GET'])
def index():
    return render_template('config.html')

@app.route('/config', methods=['GET'])
def config_page():
    return render_template('config.html')

@app.route('/logs_ui', methods=['GET'])
def show_logs():
    return render_template('logs.html')

@app.route('/pxe', methods=['GET'])
def pxe_control_page():
    return render_template('pxe_control.html')

@app.route('/clients', methods=['GET'])
def client_management_page():
    return render_template('client_management.html')

# --- API Routes ---
@app.route('/api')
def hello_world():
    return 'Welcome to the Nexus API!'

@app.route('/api/config', methods=['GET'])
def get_config():
    config = load_config()
    return jsonify(config)

@app.route('/api/config', methods=['POST'])
def update_config():
    new_config = request.get_json()
    current_config = load_config()
    current_config.update(new_config)
    save_config(current_config)
    return jsonify(current_config)

@app.route('/api/pxe/start', methods=['POST'])
def start_pxe_setup():
    try:
        check_root_privileges()
    except PermissionError as e:
        return jsonify({"error": str(e)}), 403

    config = load_config()

    if not config.get("network_interface"):
        return jsonify({"error": "The 'network_interface' key is not set in the configuration. This is currently required for PXE setup to determine the server IP, as the 'netifaces' library is unavailable for automatic detection. Please set 'network_interface' in your config (e.g., 'eth0') and try again."}), 400

    # Run perform_pxe_setup in a background thread
    thread = threading.Thread(target=perform_pxe_setup, args=(config, None))
    thread.start()

    return jsonify({"message": "PXE setup process initiated."})

@app.route('/api/logs', methods=['GET'])
def get_logs():
    logs = []
    try:
        while not api_log_queue.empty():
            logs.append(api_log_queue.get_nowait())
    except queue.Empty:
        pass # Should not happen with check, but good practice
    return jsonify(logs)

# Global variables for client listener state
client_listener_thread = None
listener_status = {"running": False, "ip": None, "port": None}
api_connected_clients = {} # {(ip, port): {"ip": ip, "port": port, "connect_time": ...}}


@app.route('/api/listener/status', methods=['GET'])
def get_listener_status():
    return jsonify(listener_status)

@app.route('/api/listener/start', methods=['POST'])
def start_listener():
    global client_listener_thread, listener_status, api_connected_clients
    config = load_config()
    server_ip = config.get("server_ip")

    if not server_ip:
        log_message("Failed to start listener: server_ip not found in configuration.", level=logging.ERROR)
        return jsonify({"error": "server_ip not found in configuration. Please set it first."}), 400

    if listener_status["running"]:
        log_message("Attempt to start listener when already running.", level=logging.WARNING)
        return jsonify({"message": "Client listener is already running.", "status": listener_status}), 200

    def on_client_connect(client_address):
        log_message(f"Client connected: {client_address[0]}:{client_address[1]}")
        api_connected_clients[client_address] = {
            "ip": client_address[0],
            "port": client_address[1],
            "connect_time": datetime.datetime.now().isoformat()
        }

    def on_data_received(client_address, data):
        log_message(f"Data received from {client_address[0]}:{client_address[1]}: {data}")
        # Further processing of data can be added here if needed

    def on_client_disconnect(client_address):
        log_message(f"Client disconnected: {client_address[0]}:{client_address[1]}")
        if client_address in api_connected_clients:
            del api_connected_clients[client_address]

    client_listener_thread = threading.Thread(
        target=start_client_listener,
        args=(
            server_ip,
            CLIENT_LISTENER_PORT,
            on_client_connect,
            on_data_received,
            on_client_disconnect,
        ),
    )
    client_listener_thread.daemon = True
    client_listener_thread.start()

    listener_status["running"] = True
    listener_status["ip"] = server_ip
    listener_status["port"] = CLIENT_LISTENER_PORT
    log_message(f"Client listener started on {server_ip}:{CLIENT_LISTENER_PORT}")
    return jsonify({"message": "Client listener started.", "status": listener_status})

@app.route('/api/listener/stop', methods=['POST'])
def stop_listener_route():
    global client_listener_thread, listener_status

    if not listener_status["running"]:
        log_message("Attempt to stop listener when not running.", level=logging.WARNING)
        return jsonify({"message": "Client listener is not running."}), 200

    try:
        log_message("Stopping client listener...")
        stop_client_listener()
        if client_listener_thread and client_listener_thread.is_alive():
            client_listener_thread.join(timeout=5)

        if client_listener_thread and client_listener_thread.is_alive():
            log_message("Client listener thread did not terminate in time.", level=logging.WARNING)
            # Potentially force stop or handle error
        else:
            log_message("Client listener thread stopped.")

        listener_status["running"] = False
        listener_status["ip"] = None
        listener_status["port"] = None
        client_listener_thread = None
        # api_connected_clients.clear() # Optionally clear clients on stop, or let them persist if server might restart
        log_message("Client listener fully stopped and status updated.")
        return jsonify({"message": "Client listener stopped.", "status": listener_status})
    except Exception as e:
        log_message(f"Error stopping client listener: {str(e)}", level=logging.ERROR)
        return jsonify({"error": f"Failed to stop client listener: {str(e)}", "status": listener_status}), 500

@app.route('/api/clients', methods=['GET'])
def get_clients():
    return jsonify(list(api_connected_clients.values()))

@app.route('/api/clients/send_message', methods=['POST'])
def send_message_to_client_route():
    data = request.get_json()
    client_ip = data.get('client_ip')
    client_port = data.get('client_port')
    message = data.get('message')

    if not all([client_ip, client_port, message]):
        log_message("Send message failed: Missing client_ip, client_port, or message.", level=logging.WARNING)
        return jsonify({"error": "Missing client_ip, client_port, or message"}), 400

    try:
        # nexus_core expects port to be an int
        target_client = (client_ip, int(client_port))

        # Check if client is in our tracked list (optional, nexus_core handles unknown clients)
        if target_client not in api_connected_clients:
             log_message(f"Sending message to client {target_client} not in api_connected_clients list. Attempting anyway.", level=logging.WARNING)

        send_data_to_client(target_client, message)
        log_message(f"Message sent to {target_client}: {message}")
        return jsonify({"message": f"Message sent to {client_ip}:{client_port}"})
    except ValueError:
        log_message(f"Send message failed: Invalid port format '{client_port}'.", level=logging.WARNING)
        return jsonify({"error": "Invalid client_port format. Must be an integer."}), 400
    except Exception as e:
        log_message(f"Error sending message to {client_ip}:{client_port}: {str(e)}", level=logging.ERROR)
        return jsonify({"error": f"Failed to send message: {str(e)}"}), 500

@app.route('/api/clients/broadcast_message', methods=['POST'])
def broadcast_message_route():
    data = request.get_json()
    message = data.get('message')

    if not message:
        log_message("Broadcast failed: Message is empty.", level=logging.WARNING)
        return jsonify({"error": "Message cannot be empty"}), 400

    try:
        # Assuming broadcast_data_to_all_clients returns number of clients it attempted to send to
        # or we can use len(api_connected_clients) if it's accurate at time of call
        num_clients = broadcast_data_to_all_clients(message)
        log_message(f"Broadcast message '{message}' sent to {num_clients} client(s).")
        # The actual number of connected clients might differ slightly if nexus_core's list is the sole authority
        # For now, we assume nexus_core.broadcast_data_to_all_clients is the source of truth for count if it returns it.
        # If it doesn't return a count, we can use len(api_connected_clients).
        # Let's assume it doesn't return a count for now.
        count_from_api_list = len(api_connected_clients)
        return jsonify({"message": "Broadcast initiated.", "attempted_clients": count_from_api_list})
    except Exception as e:
        log_message(f"Error broadcasting message: {str(e)}", level=logging.ERROR)
        return jsonify({"error": f"Failed to broadcast message: {str(e)}"}), 500

# The main execution block has been moved to nexus.py
# if __name__ == '__main__':
#     log_message("Starting Nexus API server...")
#     app.run(debug=True, host='0.0.0.0', port=5000)
